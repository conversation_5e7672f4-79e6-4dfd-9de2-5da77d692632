import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

import logging
import asyncio
import traceback
import atexit
from datetime import datetime
from typing import Dict, Any, Optional

from app.utils.textract import process_document_with_textract
from app.llm.bedrock import extract_data_with_structured_text_bedrock
from app.prompts.invoice_extraction import SYSTEM_PROMPT_EXTRACTION
from app.utils.langfuse_util import get_langfuse_util

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()  # This will output to terminal/CloudWatch
    ]
)
logger = logging.getLogger(__name__)


class DocumentExtractionProcessor:
    """
    Production-ready document extraction processor that handles:
    - Document analysis using AWS Textract
    - Intelligent data extraction using Bedrock LLM models
    - Comprehensive error handling and logging
    - Resource cleanup and management
    """

    def __init__(self, s3_client=None):
        """
        Initialize the extraction processor.

        Args:
            s3_client: Pre-configured S3 client (optional)
        """
        try:
            self.s3_client = s3_client
            self._extraction_id = None
            logger.info("✅ DocumentExtractionProcessor initialized successfully")

        except Exception as e:
            logger.error(f"❌ Failed to initialize DocumentExtractionProcessor: {str(e)}")
            logger.error(f"    Traceback: {traceback.format_exc()}")
            raise RuntimeError(f"DocumentExtractionProcessor initialization failed: {str(e)}") from e

    
    async def analyze_document_with_textract(self, s3_uri: str) -> Dict[str, Any]:
        """
        Analyze document using AWS Textract for OCR and document analysis.

        Args:
            s3_uri: S3 URI in format s3://bucket-name/object-key

        Returns:
            dict: Textract analysis results with both plain text and structured text
        """
        return await process_document_with_textract(s3_uri, s3_client=self.s3_client)
    


    async def extract_data_with_bedrock(self, system_prompt: Optional[str] = None,
                                        user_prompt: Optional[str] = None,
                                        temperature: float = 0.1,
                                        max_tokens: int = 10000) -> Dict[str, Any]:
        """
        Extract structured data using Bedrock Nova Pro model with both plain text and structured text with coordinates.

        Args:
            system_prompt: Optional custom system prompt
            user_prompt: Optional custom user prompt
            temperature: Model temperature
            max_tokens: Maximum tokens

        Returns:
            dict: Extracted structured data using coordinate-aware processing
        """
        return await extract_data_with_structured_text_bedrock(
            system_prompt, user_prompt, temperature, max_tokens
        )



    async def process_document_extraction(self, s3_uri: str) -> Dict[str, Any]:
        """
        Complete document extraction pipeline: Textract + Bedrock

        Production-ready implementation with comprehensive error handling,
        validation, and monitoring.

        Args:
            s3_uri: S3 URI of the document (must be valid s3:// format)

        Returns:
            dict: Complete extraction results including metadata

        Raises:
            ValueError: If s3_uri is invalid
            RuntimeError: If extraction pipeline fails
        """
        # Input validation
        if not s3_uri or not isinstance(s3_uri, str):
            raise ValueError("s3_uri must be a non-empty string")

        if not s3_uri.startswith('s3://'):
            raise ValueError(f"Invalid S3 URI format: {s3_uri}. Must start with 's3://'")

        # Generate unique extraction ID for tracking
        self._extraction_id = f"extraction_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{hash(s3_uri) % 10000:04d}"

        start_time = datetime.now()
        logger.info(f"🚀 Starting document extraction pipeline")
        logger.info(f"    Extraction ID: {self._extraction_id}")
        logger.info(f"    S3 URI: {s3_uri}")

        try:
            # Step 1: Analyze document with Textract
            logger.info("📋 Step 1: Document analysis with Textract")
            textract_result = await self.analyze_document_with_textract(s3_uri)

            if not textract_result:
                raise RuntimeError("Textract analysis returned empty result")

            # Step 2: Extract text from Textract result
            logger.info("📋 Step 2: Text extraction from Textract results")
            structured_text = textract_result.get('structured_text', '')

            if not structured_text:
                logger.warning("⚠️  No structured text extracted from Textract result")
                raise RuntimeError("No text content extracted from document")

            # Step 3: Extract structured data with Bedrock Nova Pro
            logger.info("📋 Step 3: Structured data extraction with Bedrock Nova Pro")
            bedrock_result = await self.extract_data_with_bedrock(
                system_prompt=SYSTEM_PROMPT_EXTRACTION,
                user_prompt=structured_text
            )

            if not bedrock_result:
                raise RuntimeError("Bedrock extraction returned empty result")

            # Compile final results
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            final_result = {
                "extraction_metadata": {
                    "extraction_id": self._extraction_id,
                    "s3_location": s3_uri,
                    "processing_start_time": start_time.isoformat(),
                    "processing_end_time": end_time.isoformat(),
                    "total_processing_time_seconds": processing_time,
                    "status": "success"
                },
                "textract_metadata": textract_result,
                "bedrock_metadata": bedrock_result.get('bedrock_metadata', {}),
                "structured_data": bedrock_result.get('structured_data', {})
            }

            logger.info("🎉 Document extraction pipeline completed successfully!")
            logger.info(f"    Extraction ID: {self._extraction_id}")
            logger.info(f"    Total processing time: {processing_time:.2f} seconds")

            return final_result

        except Exception as e:
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            logger.error("❌ Document extraction pipeline failed!")
            logger.error(f"    Extraction ID: {self._extraction_id}")
            logger.error(f"    Error Type: {type(e).__name__}")
            logger.error(f"    Error: {str(e)}")
            logger.error(f"    Processing time before failure: {processing_time:.2f} seconds")
            logger.error(f"    Failure timestamp: {end_time.isoformat()}")
            logger.error(f"    Traceback: {traceback.format_exc()}")
            logger.error("="*80)

            # Re-raise with production-friendly error message
            raise RuntimeError(f"Document extraction failed for {s3_uri}: {str(e)}") from e


# Shutdown function for Langfuse cleanup
def shutdown_langfuse():
    """Shutdown Langfuse client gracefully."""
    try:
        langfuse_util = get_langfuse_util()
        langfuse_util.shutdown()
    except Exception as e:
        logger.debug(f"Error during Langfuse shutdown: {str(e)}")


# Main function for testing and standalone execution
async def main(s3_uri: str, s3_client=None):
    """
    Main function for running the extraction pipeline.

    Production-ready implementation with proper error handling and cleanup.

    Args:
        s3_uri: S3 URI of the document to process (required)
        s3_client: Optional pre-configured S3 client

    Returns:
        dict: Complete extraction results

    Raises:
        ValueError: If s3_uri is not provided or invalid
        RuntimeError: If extraction pipeline fails
    """
    if not s3_uri:
        raise ValueError("s3_uri is required and cannot be empty")

    logger.info("🎯 Starting Document Extraction Pipeline")
    logger.info(f"    Input S3 URI: {s3_uri}")

    try:
        # Run the extraction
        processor = DocumentExtractionProcessor(s3_client=s3_client)
        result = await processor.process_document_extraction(s3_uri)

        # Log summary (safely handle missing data)
        logger.info("")
        logger.info("📊 EXTRACTION SUMMARY:")

        structured_data = result.get('structured_data', {})
        if isinstance(structured_data, dict):
            vendor_name = structured_data.get('vendor_name', 'N/A')
            invoice_number = structured_data.get('invoice_number', 'N/A')
            invoice_amount = structured_data.get('invoice_amount', 'N/A')

            logger.info(f"    Vendor: {vendor_name}")
            logger.info(f"    Invoice Number: {invoice_number}")
            logger.info(f"    Invoice Amount: {invoice_amount}")
        else:
            logger.info(f"    Raw Data: {structured_data}")

        return result

    except Exception as e:
        logger.error("")
        logger.error(f"❌ Pipeline failed with error: {str(e)}")
        logger.error(f"    Traceback: {traceback.format_exc()}")
        raise

    finally:
        # Ensure Langfuse is properly shutdown
        shutdown_langfuse()


if __name__ == "__main__":
    # Register atexit handler for cleanup
    atexit.register(shutdown_langfuse)

    # Production-ready main execution
    # S3 URI should be provided via environment variable or command line argument
    import sys

    if len(sys.argv) > 1:
        s3_uri = sys.argv[1]
        logger.info(f"Using S3 URI from command line: {s3_uri}")
    else:
        # Check environment variable
        s3_uri = os.environ.get('DOCUMENT_S3_URI')
        if s3_uri:
            logger.info(f"Using S3 URI from environment: {s3_uri}")
        else:
            logger.error("❌ No S3 URI provided. Please provide via:")
            logger.error("    1. Command line argument: python extraction.py s3://bucket/key")
            logger.error("    2. Environment variable: DOCUMENT_S3_URI=s3://bucket/key")
            sys.exit(1)

    try:
        asyncio.run(main(s3_uri))
    except KeyboardInterrupt:
        logger.info("🛑 Extraction interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ Extraction failed: {str(e)}")
        sys.exit(1)
