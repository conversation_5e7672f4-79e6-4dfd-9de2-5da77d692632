"""
Langfuse Utility Class

A simple utility class for initializing Langfuse, creating generations, and ending generations.
Inspired by the provided example code for structured data extraction.
"""

from typing import Optional, Dict, Any, List
import logging
import socket
import platform
import os
import getpass
import requests
from datetime import datetime

from langfuse import Langfuse
from app.core.configuration import settings

logger = logging.getLogger(__name__)


class LangfuseUtil:
    """
    Simple utility class for Langfuse operations including initialization,
    generation creation, and generation ending.
    """

    def __init__(self):
        """Initialize Langfuse client with settings from configuration."""
        self.langfuse = None
        self._initialize_langfuse()

    def _initialize_langfuse(self) -> None:
        """Initialize Langfuse client using settings from configuration."""
        try:
            if not settings.LANGFUSE_ENABLED:
                logger.debug("<PERSON><PERSON> is disabled in settings")
                return

            if not all([settings.LANGFUSE_SECRET_KEY, settings.LANGFUSE_PUBLIC_KEY, settings.LANGFUSE_HOST]):
                logger.debug("Langfuse configuration incomplete - missing required keys")
                return

            # Lazy initialization - only create client when needed
            # This prevents blocking during import/initialization
            self._langfuse_config = {
                'secret_key': settings.LANGFUSE_SECRET_KEY,
                'public_key': settings.LANGFUSE_PUBLIC_KEY,
                'host': settings.LANGFUSE_HOST,
                'flush_at': settings.LANGFUSE_FLUSH_AT,
                'flush_interval': settings.LANGFUSE_FLUSH_INTERVAL
            }

            # Don't initialize the actual client here to avoid blocking
            self.langfuse = None
            self._langfuse_ready = False

            logger.debug("Langfuse configuration loaded successfully")

        except Exception as e:
            logger.debug(f"Failed to load Langfuse configuration: {str(e)}, continuing without it")
            self.langfuse = None
            self._langfuse_config = None
            self._langfuse_ready = False

    def _ensure_langfuse_client(self) -> bool:
        """Ensure Langfuse client is initialized when needed."""
        if self.langfuse is not None:
            return True

        if not hasattr(self, '_langfuse_config') or self._langfuse_config is None:
            return False

        try:
            from langfuse import Langfuse
            self.langfuse = Langfuse(**self._langfuse_config)
            self._langfuse_ready = True
            logger.debug("Langfuse client initialized on demand")
            return True
        except Exception as e:
            logger.debug(f"Failed to initialize Langfuse client: {str(e)}")
            self._langfuse_ready = False
            return False

    @property
    def is_enabled(self) -> bool:
        """Check if Langfuse is enabled and can be initialized."""
        return hasattr(self, '_langfuse_config') and self._langfuse_config is not None

    def _get_user_metadata(self) -> Dict[str, Any]:
        """
        Collect metadata about the user's environment for tracking purposes.

        Returns:
            Dictionary containing user environment metadata
        """
        metadata = {}

        try:
            # Basic system information
            metadata["hostname"] = socket.gethostname()
            metadata["platform"] = platform.platform()
            metadata["system"] = platform.system()
            metadata["machine"] = platform.machine()
            metadata["processor"] = platform.processor()
            metadata["python_version"] = platform.python_version()

            # User information
            metadata["username"] = getpass.getuser()

            # Environment variables (selective)
            metadata["environment"] = settings.ENVIRONMENT if hasattr(settings, 'ENVIRONMENT') else 'unknown'

            # Working directory
            metadata["working_directory"] = os.getcwd()

            # Timestamp
            metadata["session_start"] = datetime.now().isoformat()

        except Exception as e:
            logger.warning(f"Failed to collect some metadata: {str(e)}")

        try:
            # Try to get local IP address
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                # Connect to a remote address (doesn't actually send data)
                s.connect(("*******", 80))
                metadata["local_ip"] = s.getsockname()[0]
        except Exception as e:
            logger.debug(f"Could not determine local IP: {str(e)}")
            metadata["local_ip"] = "unknown"

        try:
            # Try to get public IP address (with timeout)
            response = requests.get("https://api.ipify.org?format=json", timeout=3)
            if response.status_code == 200:
                metadata["public_ip"] = response.json().get("ip", "unknown")
            else:
                metadata["public_ip"] = "unknown"
        except Exception as e:
            logger.debug(f"Could not determine public IP: {str(e)}")
            metadata["public_ip"] = "unknown"

        # Add application-specific metadata
        metadata["application"] = settings.PROJECT_NAME if hasattr(settings, 'PROJECT_NAME') else 'document-data-extraction'
        metadata["aws_region"] = settings.AWS_REGION if hasattr(settings, 'AWS_REGION') else 'unknown'

        return metadata

    def create_generation(
        self,
        name: str,
        model: str,
        system_prompt: Optional[str] = None,
        user_prompt: Optional[str] = None,
        input_messages: Optional[List[Dict[str, str]]] = None,
        model_parameters: Optional[Dict[str, Any]] = None,
        include_user_metadata: bool = True
    ) -> Optional[Any]:
        """
        Create a standalone generation without trace context.

        Args:
            name: Name of the generation (e.g., "generation_extraction")
            model: Model identifier (e.g., "amazon.nova-pro-v1:0")
            system_prompt: System prompt content
            user_prompt: User prompt content
            input_messages: Pre-formatted input messages (alternative to system/user prompts)
            model_parameters: Model parameters like temperature, max_tokens, top_p
            include_user_metadata: Whether to include user environment metadata (default: True)

        Returns:
            Generation object if successful, None otherwise
        """
        if not self.is_enabled:
            logger.debug("Cannot create generation - Langfuse not enabled")
            return None

        if not self._ensure_langfuse_client():
            logger.debug("Cannot create generation - Langfuse client initialization failed")
            return None

        try:
            # Prepare input messages
            if input_messages:
                messages = input_messages
            else:
                messages = []
                if system_prompt:
                    messages.append({"role": "system", "content": system_prompt})
                if user_prompt:
                    messages.append({"role": "user", "content": user_prompt})

            # Default model parameters
            default_params = {
                "temperature": 0.1,
                "max_tokens": 4000,
                "top_p": 0.9
            }

            # Merge with provided parameters
            final_params = {**default_params, **(model_parameters or {})}

            # Collect user metadata if enabled
            metadata = {}
            if include_user_metadata:
                metadata = self._get_user_metadata()
                logger.debug(f"Including user metadata: hostname={metadata.get('hostname')}, "
                           f"username={metadata.get('username')}, "
                           f"local_ip={metadata.get('local_ip')}")

            # Create generation with metadata
            if self.langfuse:
                generation = self.langfuse.generation(
                    name=name,
                    model=model,
                    input=messages,
                    model_parameters=final_params,
                    metadata=metadata
                )
            else:
                generation = None

            logger.debug(f"Created generation: {name} with metadata")
            return generation

        except Exception as e:
            logger.error(f"Failed to create generation '{name}': {str(e)}")
            return None

    def create_trace(
        self,
        name: str,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        include_user_metadata: bool = True,
        additional_metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[Any]:
        """
        Create a trace with user metadata for comprehensive tracking.

        Args:
            name: Name of the trace
            user_id: Optional user identifier
            session_id: Optional session identifier
            include_user_metadata: Whether to include user environment metadata (default: True)
            additional_metadata: Additional metadata to include

        Returns:
            Trace object if successful, None otherwise
        """
        if not self.is_enabled:
            logger.debug("Cannot create trace - Langfuse not enabled")
            return None

        if not self._ensure_langfuse_client():
            logger.debug("Cannot create trace - Langfuse client initialization failed")
            return None

        try:
            # Collect metadata
            metadata = additional_metadata or {}

            if include_user_metadata:
                user_metadata = self._get_user_metadata()
                metadata.update(user_metadata)

            # Use hostname as user_id if not provided
            if not user_id and include_user_metadata:
                user_id = f"{metadata.get('username', 'unknown')}@{metadata.get('hostname', 'unknown')}"

            # Create trace
            if self.langfuse:
                trace = self.langfuse.trace(
                    name=name,
                    user_id=user_id,
                    session_id=session_id,
                    metadata=metadata
                )
            else:
                trace = None

            logger.debug(f"Created trace: {name} for user: {user_id}")
            return trace

        except Exception as e:
            logger.error(f"Failed to create trace '{name}': {str(e)}")
            return None

    def end_generation(
        self,
        generation: Any,
        output: str,
        usage_details: Optional[Dict[str, int]] = None,
        level: str = "DEFAULT",
        status_message: Optional[str] = None
    ) -> None:
        """
        End a generation with output and usage details.

        Args:
            generation: The generation object to end
            output: The output from the model
            usage_details: Token usage details with keys: input, output, total
            level: Level for the generation (DEFAULT, ERROR, etc.)
            status_message: Optional status message for errors
        """
        if generation is None:
            logger.warning("Cannot end generation - generation object is None")
            return

        try:
            end_params: Dict[str, Any] = {"output": output}

            # Add usage details if provided
            if usage_details:
                end_params["usage_details"] = usage_details

            # Add level and status message for errors
            if level != "DEFAULT":
                end_params["level"] = level

            if status_message:
                end_params["status_message"] = status_message

            generation.end(**end_params)
            logger.debug("Generation ended successfully")

        except Exception as e:
            logger.error(f"Failed to end generation: {str(e)}")

    def flush(self) -> None:
        """Ensure data is flushed to Langfuse."""
        if not self.is_enabled:
            return

        if not self._ensure_langfuse_client():
            return

        try:
            if self.langfuse:
                self.langfuse.flush()
                logger.debug("Langfuse data flushed")

        except Exception as e:
            logger.debug(f"Failed to flush Langfuse data: {str(e)}")

    def shutdown(self) -> None:
        """
        Shutdown Langfuse client gracefully without blocking.

        Production-ready implementation that ensures proper cleanup
        without hanging the application.
        """
        if not self.is_enabled:
            logger.debug("Langfuse not enabled, skipping shutdown")
            return

        try:
            logger.debug("Starting non-blocking Langfuse shutdown")

            # Quick flush without waiting
            if self.langfuse:
                try:
                    self.langfuse.flush()
                except Exception as e:
                    logger.debug(f"Flush error during shutdown: {str(e)}")

                # Set client to None to prevent further use
                self.langfuse = None
                logger.debug("Langfuse client cleared")
            else:
                logger.debug("Langfuse client is None, nothing to shutdown")

        except Exception as e:
            logger.debug(f"Error during Langfuse shutdown (non-critical): {str(e)}")
            # Don't raise exception as this is cleanup code


# Global instance for easy access
_langfuse_util_instance = None


def get_langfuse_util() -> LangfuseUtil:
    """Get singleton instance of LangfuseUtil."""
    global _langfuse_util_instance
    if _langfuse_util_instance is None:
        _langfuse_util_instance = LangfuseUtil()
    return _langfuse_util_instance