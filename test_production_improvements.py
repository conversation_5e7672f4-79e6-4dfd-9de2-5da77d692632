#!/usr/bin/env python3
"""
Production readiness test script for document extraction pipeline.

This script validates the production improvements made to:
- extraction.py
- textract.py  
- bedrock.py
- langfuse_util.py

Run this script to verify the improvements work correctly.
"""

import sys
import os
import asyncio
import logging
from unittest.mock import Mock, patch

# Add the app directory to Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), 'app')))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_imports():
    """Test that all modules can be imported successfully."""
    logger.info("🧪 Testing imports...")

    try:
        global DocumentExtractionProcessor, TextractProcessor, BedrockProcessor, LangfuseUtil, get_langfuse_util

        from app.llm.extraction import DocumentExtractionProcessor
        from app.utils.textract import TextractProcessor
        from app.llm.bedrock import BedrockProcessor
        from app.utils.langfuse_util import LangfuseUtil, get_langfuse_util
        logger.info("✅ All imports successful")
        return True
    except Exception as e:
        logger.error(f"❌ Import failed: {str(e)}")
        return False


def test_langfuse_util():
    """Test LangfuseUtil initialization and basic functionality."""
    logger.info("🧪 Testing LangfuseUtil...")
    
    try:
        # Test initialization
        langfuse_util = get_langfuse_util()
        
        # Test basic properties
        is_enabled = langfuse_util.is_enabled
        logger.info(f"    Langfuse enabled: {is_enabled}")
        
        # Test shutdown (should not raise exception)
        langfuse_util.shutdown()
        
        logger.info("✅ LangfuseUtil test passed")
        return True
    except Exception as e:
        logger.error(f"❌ LangfuseUtil test failed: {str(e)}")
        return False


def test_bedrock_processor():
    """Test BedrockProcessor initialization."""
    logger.info("🧪 Testing BedrockProcessor...")
    
    try:
        # Test initialization with default settings
        processor = BedrockProcessor()
        
        # Test basic properties
        assert hasattr(processor, 'region')
        assert hasattr(processor, 'model_id')
        assert hasattr(processor, 'bedrock_client')
        
        logger.info(f"    Region: {processor.region}")
        logger.info(f"    Model ID: {processor.model_id}")
        logger.info("✅ BedrockProcessor test passed")
        return True
    except Exception as e:
        logger.error(f"❌ BedrockProcessor test failed: {str(e)}")
        return False


def test_textract_processor():
    """Test TextractProcessor initialization."""
    logger.info("🧪 Testing TextractProcessor...")
    
    try:
        # Test initialization
        processor = TextractProcessor()
        
        # Test basic properties
        assert hasattr(processor, 'region')
        assert hasattr(processor, 'textract_client')
        assert hasattr(processor, 's3_client')
        assert hasattr(processor, 'supported_formats')
        
        logger.info(f"    Region: {processor.region}")
        logger.info(f"    Supported formats: {processor.supported_formats}")
        logger.info("✅ TextractProcessor test passed")
        return True
    except Exception as e:
        logger.error(f"❌ TextractProcessor test failed: {str(e)}")
        return False


def test_extraction_processor():
    """Test DocumentExtractionProcessor initialization."""
    logger.info("🧪 Testing DocumentExtractionProcessor...")
    
    try:
        # Test initialization
        processor = DocumentExtractionProcessor()
        
        # Test basic properties
        assert hasattr(processor, 's3_client')
        assert hasattr(processor, '_extraction_id')
        
        logger.info("✅ DocumentExtractionProcessor test passed")
        return True
    except Exception as e:
        logger.error(f"❌ DocumentExtractionProcessor test failed: {str(e)}")
        return False


def test_input_validation():
    """Test input validation in extraction processor."""
    logger.info("🧪 Testing input validation...")
    
    try:
        from app.llm.extraction import DocumentExtractionProcessor
        
        processor = DocumentExtractionProcessor()
        
        # Test invalid S3 URI
        async def test_invalid_uri():
            try:
                await processor.process_document_extraction("")
                return False  # Should have raised exception
            except ValueError as e:
                logger.info(f"    Correctly caught ValueError: {str(e)}")
                return True
            except Exception as e:
                logger.error(f"    Unexpected exception: {str(e)}")
                return False
        
        # Test invalid S3 URI format
        async def test_invalid_format():
            try:
                await processor.process_document_extraction("invalid-uri")
                return False  # Should have raised exception
            except ValueError as e:
                logger.info(f"    Correctly caught format error: {str(e)}")
                return True
            except Exception as e:
                logger.error(f"    Unexpected exception: {str(e)}")
                return False
        
        # Run async tests
        result1 = asyncio.run(test_invalid_uri())
        result2 = asyncio.run(test_invalid_format())
        
        if result1 and result2:
            logger.info("✅ Input validation test passed")
            return True
        else:
            logger.error("❌ Input validation test failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Input validation test failed: {str(e)}")
        return False


def main():
    """Run all production readiness tests."""
    logger.info("🚀 Starting production readiness tests...")
    
    tests = [
        test_imports,
        test_langfuse_util,
        test_bedrock_processor,
        test_textract_processor,
        test_extraction_processor,
        test_input_validation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                logger.error(f"Test {test.__name__} failed")
        except Exception as e:
            logger.error(f"Test {test.__name__} crashed: {str(e)}")
    
    logger.info("")
    logger.info("📊 TEST SUMMARY:")
    logger.info(f"    Passed: {passed}/{total}")
    logger.info(f"    Failed: {total - passed}/{total}")
    
    if passed == total:
        logger.info("🎉 All production readiness tests passed!")
        return 0
    else:
        logger.error("❌ Some tests failed. Please review the issues above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
