#!/usr/bin/env python3
"""
Simple production readiness test for document extraction pipeline.
"""

import sys
import os
import logging

# Add the app directory to Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), 'app')))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_basic_functionality():
    """Test basic functionality without external dependencies."""
    logger.info("🧪 Testing basic functionality...")
    
    try:
        # Test imports
        from app.llm.extraction import DocumentExtractionProcessor
        from app.utils.textract import TextractProcessor
        from app.llm.bedrock import BedrockProcessor
        from app.utils.langfuse_util import LangfuseUtil
        logger.info("✅ All imports successful")
        
        # Test DocumentExtractionProcessor initialization
        processor = DocumentExtractionProcessor()
        assert hasattr(processor, '_extraction_id')
        logger.info("✅ DocumentExtractionProcessor initialization successful")
        
        # Test TextractProcessor initialization
        textract_processor = TextractProcessor()
        assert hasattr(textract_processor, 'supported_formats')
        assert hasattr(textract_processor, 'max_file_size')
        logger.info("✅ TextractProcessor initialization successful")
        
        # Test BedrockProcessor initialization
        bedrock_processor = BedrockProcessor()
        assert hasattr(bedrock_processor, 'model_id')
        assert bedrock_processor.model_id == "amazon.nova-pro-v1:0"
        logger.info("✅ BedrockProcessor initialization successful")
        
        # Test LangfuseUtil initialization (without shutdown)
        langfuse_util = LangfuseUtil()
        assert hasattr(langfuse_util, 'is_enabled')
        logger.info("✅ LangfuseUtil initialization successful")
        
        logger.info("🎉 All basic functionality tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False


def test_error_handling():
    """Test error handling improvements."""
    logger.info("🧪 Testing error handling...")
    
    try:
        from app.llm.extraction import DocumentExtractionProcessor
        
        processor = DocumentExtractionProcessor()
        
        # Test ValueError for empty string
        try:
            import asyncio
            asyncio.run(processor.process_document_extraction(""))
            logger.error("Should have raised ValueError")
            return False
        except ValueError as e:
            logger.info(f"✅ Correctly caught ValueError: {str(e)}")
        
        # Test ValueError for invalid format
        try:
            asyncio.run(processor.process_document_extraction("invalid-format"))
            logger.error("Should have raised ValueError")
            return False
        except ValueError as e:
            logger.info(f"✅ Correctly caught format error: {str(e)}")
        
        logger.info("✅ Error handling tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error handling test failed: {str(e)}")
        return False


def test_configuration_validation():
    """Test configuration validation."""
    logger.info("🧪 Testing configuration validation...")
    
    try:
        from app.core.configuration import settings
        
        # Check that required settings exist
        assert hasattr(settings, 'AWS_REGION')
        assert hasattr(settings, 'AWS_ACCESS_KEY_ID')
        assert hasattr(settings, 'AWS_SECRET_ACCESS_KEY')
        assert hasattr(settings, 'LANGFUSE_ENABLED')
        
        logger.info(f"✅ AWS Region: {settings.AWS_REGION}")
        logger.info(f"✅ Langfuse Enabled: {settings.LANGFUSE_ENABLED}")
        logger.info("✅ Configuration validation passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Configuration validation failed: {str(e)}")
        return False


def main():
    """Run production readiness tests."""
    logger.info("🚀 Starting production readiness tests...")
    
    tests = [
        test_basic_functionality,
        test_error_handling,
        test_configuration_validation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            logger.error(f"Test {test.__name__} failed")
    
    logger.info("")
    logger.info("📊 TEST SUMMARY:")
    logger.info(f"    Passed: {passed}/{total}")
    logger.info(f"    Failed: {total - passed}/{total}")
    
    if passed == total:
        logger.info("🎉 All production readiness tests passed!")
        return 0
    else:
        logger.error("❌ Some tests failed.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
