#!/usr/bin/env python3
"""
Test script to verify Lang<PERSON> initialization doesn't hang.
"""

import sys
import os
import time
import logging

# Add the app directory to Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), 'app')))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_langfuse_initialization():
    """Test that Langfuse initialization is fast and doesn't hang."""
    logger.info("🧪 Testing Langfuse initialization speed...")
    
    start_time = time.time()
    
    try:
        from app.utils.langfuse_util import get_langfuse_util
        
        # This should be very fast now (lazy initialization)
        langfuse_util = get_langfuse_util()
        
        init_time = time.time() - start_time
        logger.info(f"✅ Langfuse utility initialized in {init_time:.3f} seconds")
        
        # Test basic properties
        is_enabled = langfuse_util.is_enabled
        logger.info(f"    Langfuse enabled: {is_enabled}")
        
        # Test that we can create a generation (this will trigger actual client init)
        if is_enabled:
            generation_start = time.time()
            generation = langfuse_util.create_generation(
                name="test_generation",
                model="test-model",
                system_prompt="test system prompt",
                user_prompt="test user prompt"
            )
            generation_time = time.time() - generation_start
            logger.info(f"    Generation creation took {generation_time:.3f} seconds")
            
            if generation:
                logger.info("✅ Generation created successfully")
            else:
                logger.info("ℹ️  Generation creation returned None (expected if Langfuse not configured)")
        
        # Test flush (should be fast)
        flush_start = time.time()
        langfuse_util.flush()
        flush_time = time.time() - flush_start
        logger.info(f"    Flush took {flush_time:.3f} seconds")
        
        # Test shutdown (should be fast)
        shutdown_start = time.time()
        langfuse_util.shutdown()
        shutdown_time = time.time() - shutdown_start
        logger.info(f"    Shutdown took {shutdown_time:.3f} seconds")
        
        total_time = time.time() - start_time
        logger.info(f"✅ Total test time: {total_time:.3f} seconds")
        
        # Verify it's reasonably fast (should be under 5 seconds even with network calls)
        if total_time < 5.0:
            logger.info("🎉 Langfuse initialization is fast and production-ready!")
            return True
        else:
            logger.warning(f"⚠️  Langfuse operations took {total_time:.3f}s, might be slow for production")
            return True  # Still pass, just warn
        
    except Exception as e:
        logger.error(f"❌ Langfuse test failed: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False


def main():
    """Run the Langfuse fix test."""
    logger.info("🚀 Testing Langfuse initialization fix...")
    
    if test_langfuse_initialization():
        logger.info("🎉 Langfuse fix test passed!")
        return 0
    else:
        logger.error("❌ Langfuse fix test failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
